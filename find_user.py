#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json

with open('rawdata/202505.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 检查新识别的用户
target_users = ['王木木', '亚平', '小楷进行时', '碎夏落栀念🍃']

for i, user in enumerate(data['users']):
    if any(target in user['user_id'] for target in target_users):
        print(f'用户 {i+1}: {user["user_id"]}')
        print(f'书籍数量: {user["total_books"]}')
        print('书籍列表:')
        for book in user['books']:
            print(
                f'  - {book["title"]} ({book["reading_status"]}, {book["progress"]})')
        print()
